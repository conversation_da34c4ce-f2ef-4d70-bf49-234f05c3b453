# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to Firebase Hosting on PR
on: pull_request
permissions:
  checks: write
  contents: read
  pull-requests: write
jobs:
  build_and_preview:
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"

      # Install pnpm
      - name: Install pnpm
        run: npm install -g pnpm

      # Install dependencies
      - name: Install dependencies
        run: pnpm install

      # Install functions dependencies
      - name: Install functions dependencies
        run: |
          cd functions
          npm install
          cd ..

      # Build project
      - name: Build project
        run: CI=false pnpm build
        env:
          CI: false

      # Deploy preview (hosting only for PRs)
      - uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_ZERO2AGENT_FFE2E }}
          projectId: zero2agent-ffe2e
