name: Deploy to Firebase Hosting and Functions on merge

on:
  push:
    branches:
      - main

jobs:
  build_and_deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"
          cache: "npm"

      - name: Install pnpm
        run: npm install -g pnpm

      - name: Install frontend dependencies
        run: pnpm install

      - name: Install Firebase CLI
        run: npm install -g firebase-tools

      - name: Install function dependencies
        run: |
          cd functions
          npm install
          cd ..

      - name: Build frontend
        run: pnpm build
        env:
          CI: false

      - name: Authenticate to Firebase
        run: |
          echo '${{ secrets.FIREBASE_SERVICE_ACCOUNT_ZERO2AGENT_FFE2E }}' > firebase-service-account.json
          export GOOGLE_APPLICATION_CREDENTIALS=firebase-service-account.json
          firebase use zero2agent-ffe2e

      - name: Deploy Firebase Functions
        run: firebase deploy --only functions
        env:
          GOOGLE_APPLICATION_CREDENTIALS: firebase-service-account.json

      - name: Deploy Firebase Hosting
        uses: FirebaseExtended/action-hosting-deploy@v0
        with:
          repoToken: ${{ secrets.GITHUB_TOKEN }}
          firebaseServiceAccount: ${{ secrets.FIREBASE_SERVICE_ACCOUNT_ZERO2AGENT_FFE2E }}
          channelId: live
          projectId: zero2agent-ffe2e

      - name: Cleanup
        run: rm -f firebase-service-account.json
